<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify AI 聊天助手</title>
    <!-- 引入 Tailwind CSS 以实现现代化设计 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入 Marked.js 库来解析 Markdown 文本 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- 引入 DOMPurify 来净化 HTML 内容，防止 XSS 攻击 -->
    <script src="https://cdn.jsdelivr.net/npm/dompurify@2.3.8/dist/purify.min.js"></script>
    <style>
        /* 自定义样式 */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
        }
        /* 自定义滚动条样式，以匹配深色主题 */
        #chat-container::-webkit-scrollbar {
            width: 8px;
        }
        #chat-container::-webkit-scrollbar-track {
            background: #1f2937; /* 深灰色背景 */
        }
        #chat-container::-webkit-scrollbar-thumb {
            background-color: #4b5563; /* 中灰色滑块 */
            border-radius: 10px;
            border: 2px solid #1f2937;
        }
        /* 打字动画效果 */
        .typing-indicator span {
            height: 8px;
            width: 8px;
            background-color: #9ca3af;
            display: inline-block;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out both;
        }
        .typing-indicator span:nth-child(1) { animation-delay: 0s; }
        .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
        .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1.0); }
        }
    </style>
</head>
<body class="bg-gray-900 text-white flex flex-col h-screen">

    <!-- 页面标题 -->
    <header class="bg-gray-800/50 backdrop-blur-sm border-b border-gray-700 p-4 text-center shadow-lg">
        <h1 class="text-xl font-bold">Dify AI 聊天助手</h1>
        <p class="text-sm text-gray-400">输入您的需求，同时获取文本与图片回复</p>
    </header>

    <!-- 聊天消息容器 -->
    <main id="chat-container" class="flex-1 overflow-y-auto p-4 md:p-6 space-y-6">
        <!-- 初始欢迎消息 -->
        <div class="chat-message bot-message flex items-start gap-3">
            <div class="bg-gray-700 rounded-full h-8 w-8 flex items-center justify-center text-sm font-bold flex-shrink-0">AI</div>
            <div class="bg-gray-800 rounded-lg p-4 max-w-2xl">
                <p>您好！有什么可以帮助您的吗？请告诉我您的需求，例如：“帮我画一只在月球上奔跑的猫”。</p>
            </div>
        </div>
    </main>

    <!-- 输入区域 -->
    <footer class="bg-gray-800 border-t border-gray-700 p-4">
        <div class="max-w-3xl mx-auto">
            <form id="chat-form" class="flex items-center gap-3">
                <textarea 
                    id="prompt-input"
                    placeholder="请输入您的需求..."
                    class="flex-1 bg-gray-700 border border-gray-600 rounded-lg p-3 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200"
                    rows="1"
                    oninput="this.style.height = 'auto'; this.style.height = (this.scrollHeight) + 'px';"
                ></textarea>
                <button 
                    type="submit" 
                    id="submit-button"
                    class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white font-bold rounded-lg p-3 flex items-center justify-center transition duration-200"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-send"><line x1="22" y1="2" x2="11" y2="13"></line><polygon points="22 2 15 22 11 13 2 9 22 2"></polygon></svg>
                </button>
            </form>
        </div>
    </footer>

    <!-- JavaScript 逻辑 -->
    <script>
        // DOM 元素引用
        const chatContainer = document.getElementById('chat-container');
        const chatForm = document.getElementById('chat-form');
        const promptInput = document.getElementById('prompt-input');
        const submitButton = document.getElementById('submit-button');

        // Dify API 配置
        const DIFY_API_URL = 'https://api.dify.ai/v1/chat-messages';
        const DIFY_API_KEY = 'app-yCI5iQRExdaLayJYlNbwmEXg'; // 您提供的 API Key

        // 从本地存储获取会话 ID。如果不存在，则为 null。
        let conversationId = localStorage.getItem('dify_conversation_id');
        
        // 为最终用户创建或获取一个持久的唯一 ID。
        let userId = localStorage.getItem('dify_user_id');
        if (!userId) {
            userId = `user_${crypto.randomUUID()}`;
            localStorage.setItem('dify_user_id', userId);
        }

        // 处理表单提交
        chatForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const prompt = promptInput.value.trim();
            if (!prompt) return;

            // 显示用户消息
            displayUserMessage(prompt);
            promptInput.value = '';
            promptInput.style.height = 'auto'; // 重置高度

            // 禁用输入框并显示加载状态
            toggleInput(true);
            displayTypingIndicator();

            try {
                // 构造请求体。如果 conversationId 存在，则包含它。
                const payload = {
                    inputs: {},
                    query: prompt,
                    user: userId,
                    response_mode: 'blocking',
                };
                if (conversationId) {
                    payload.conversation_id = conversationId;
                }

                // 调用 Dify API
                let response = await fetch(DIFY_API_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${DIFY_API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });

                let data;

                // 检查响应是否成功
                if (!response.ok) {
                    const errorData = await response.json();
                    // 特别处理“会话不存在”的错误
                    if (errorData.code === 'conversation_not_exists') {
                        console.warn("会话 ID 无效，正在开始一个新会话。");
                        // 清除无效的会话 ID 并重试
                        localStorage.removeItem('dify_conversation_id');
                        conversationId = null;
                        
                        // 从请求体中移除 conversation_id 字段以开始新会话
                        delete payload.conversation_id;

                        response = await fetch(DIFY_API_URL, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${DIFY_API_KEY}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(payload)
                        });

                        // 如果重试仍然失败，则抛出错误
                        if (!response.ok) {
                            const newErrorData = await response.json();
                            throw new Error(`API 请求失败 (重试后): ${newErrorData.message || response.statusText}`);
                        }
                    } else {
                        // 如果是其他错误，则直接抛出
                        throw new Error(`API 请求失败: ${errorData.message || response.statusText}`);
                    }
                }
                
                data = await response.json();
                
                // 从每个成功的响应中更新/设置会话 ID，以保持会话连续性
                conversationId = data.conversation_id;
                localStorage.setItem('dify_conversation_id', conversationId);

                // 从回复中解析文本和图片
                const { text, imageUrl } = parseDifyResponse(data.answer);

                // 显示 AI 回复
                displayBotMessage(text, imageUrl);

            } catch (error) {
                console.error('API 调用出错:', error);
                displayBotMessage(`抱歉，发生了一个错误：${error.message}`, null);
            } finally {
                // 无论成功或失败，都移除加载状态并重新启用输入
                removeTypingIndicator();
                toggleInput(false);
            }
        });

        // 监听输入框的回车键，实现快速发送
        promptInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                chatForm.dispatchEvent(new Event('submit'));
            }
        });

        // 切换输入框和按钮的禁用状态
        function toggleInput(disabled) {
            promptInput.disabled = disabled;
            submitButton.disabled = disabled;
        }

        // 显示用户消息到聊天窗口
        function displayUserMessage(prompt) {
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message user-message flex items-start gap-3 justify-end';
            
            const sanitizedPrompt = DOMPurify.sanitize(prompt);

            messageElement.innerHTML = `
                <div class="bg-blue-600 rounded-lg p-4 max-w-2xl">
                    <p>${sanitizedPrompt.replace(/\n/g, '<br>')}</p>
                </div>
                <div class="bg-gray-700 rounded-full h-8 w-8 flex items-center justify-center text-sm font-bold flex-shrink-0">You</div>
            `;
            chatContainer.appendChild(messageElement);
            scrollToBottom();
        }

        // 显示 AI 消息到聊天窗口
        function displayBotMessage(text, imageUrl) {
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message bot-message flex items-start gap-3';
            
            // 使用 Marked.js 将 Markdown 格式的文本转换为 HTML
            const unsafeHtml = marked.parse(text || "无法生成文本回复。");
            // 使用 DOMPurify 清理 HTML，防止 XSS
            const safeHtml = DOMPurify.sanitize(unsafeHtml);

            let imageHtml = '';
            if (imageUrl) {
                imageHtml = `
                    <div class="mt-3">
                        <img src="${imageUrl}" alt="AI 生成的图片" class="rounded-lg max-w-full h-auto" onload="scrollToBottom()">
                    </div>
                `;
            }

            messageElement.innerHTML = `
                <div class="bg-gray-700 rounded-full h-8 w-8 flex items-center justify-center text-sm font-bold flex-shrink-0">AI</div>
                <div class="bg-gray-800 rounded-lg p-4 max-w-2xl w-full">
                    <div class="prose prose-invert max-w-none">${safeHtml}</div>
                    ${imageHtml}
                </div>
            `;
            chatContainer.appendChild(messageElement);
            scrollToBottom();
        }

        // 显示“正在输入”的动画
        function displayTypingIndicator() {
            const indicator = document.createElement('div');
            indicator.id = 'typing-indicator';
            indicator.className = 'chat-message bot-message flex items-start gap-3';
            indicator.innerHTML = `
                <div class="bg-gray-700 rounded-full h-8 w-8 flex items-center justify-center text-sm font-bold flex-shrink-0">AI</div>
                <div class="bg-gray-800 rounded-lg p-4 max-w-2xl">
                    <div class="typing-indicator">
                        <span></span><span></span><span></span>
                    </div>
                </div>
            `;
            chatContainer.appendChild(indicator);
            scrollToBottom();
        }

        // 移除“正在输入”的动画
        function removeTypingIndicator() {
            const indicator = document.getElementById('typing-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        /**
         * 解析 Dify 的回复，分离文本和图片URL。
         * 假设图片以 Markdown 格式 `![...](...)` 提供。
         * @param {string} answer - Dify API 返回的 answer 字段内容。
         * @returns {{text: string, imageUrl: string|null}}
         */
        function parseDifyResponse(answer) {
            if (!answer) return { text: '', imageUrl: null };

            const imageRegex = /!\[.*?\]\((.*?)\)/;
            const match = answer.match(imageRegex);

            if (match && match[1]) {
                const imageUrl = match[1];
                // 移除 Markdown 图片链接，保留纯文本
                const text = answer.replace(imageRegex, '').trim();
                return { text, imageUrl };
            } else {
                // 如果没有找到图片，则整个回复都是文本
                return { text: answer, imageUrl: null };
            }
        }

        // 自动滚动到聊天窗口底部
        function scrollToBottom() {
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // 页面加载后，让输入框自动获得焦点
        window.onload = () => {
            promptInput.focus();
        };
    </script>
</body>
</html>
