# Zuojian Image - Dify AI 聊天助手

一个基于 Dify AI 的智能聊天助手，支持文本对话和图片生成功能。

## 🌟 功能特性

- **智能对话**: 基于 Dify AI 平台的强大语言模型
- **图片生成**: 支持根据文本描述生成图片
- **现代化界面**: 使用 Tailwind CSS 构建的响应式深色主题界面
- **会话持续**: 自动保存会话状态，支持连续对话
- **Markdown 支持**: 支持 Markdown 格式的文本渲染
- **安全防护**: 集成 DOMPurify 防止 XSS 攻击
- **实时反馈**: 打字动画和加载状态提示

## 🚀 快速开始

### 在线访问

直接在浏览器中打开 `index.html` 文件即可使用。

### 本地部署

1. 克隆项目到本地：
```bash
git clone https://github.com/jememouse/zuojian-image.git
cd zuojian-image
```

2. 使用任意 HTTP 服务器运行项目：

**使用 Python (推荐)**：
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**使用 Node.js**：
```bash
npx serve .
```

**使用 PHP**：
```bash
php -S localhost:8000
```

3. 在浏览器中访问 `http://localhost:8000`

## 🛠️ 技术栈

- **前端框架**: 纯 HTML/CSS/JavaScript
- **样式框架**: Tailwind CSS
- **Markdown 解析**: Marked.js
- **安全防护**: DOMPurify
- **AI 平台**: Dify AI API

## 📝 使用说明

1. **发送消息**: 在输入框中输入您的需求，按回车键或点击发送按钮
2. **图片生成**: 输入图片描述，如"帮我画一只在月球上奔跑的猫"
3. **快捷操作**: 
   - 按 `Enter` 发送消息
   - 按 `Shift + Enter` 换行
4. **会话管理**: 应用会自动保存会话状态，刷新页面后可继续对话

## ⚙️ 配置说明

### API 配置

在 `index.html` 文件中找到以下配置项：

```javascript
// Dify API 配置
const DIFY_API_URL = 'https://api.dify.ai/v1/chat-messages';
const DIFY_API_KEY = 'your-api-key-here'; // 替换为您的 API Key
```

### 自定义样式

项目使用 Tailwind CSS，您可以通过修改 CSS 类来自定义界面样式。主要的样式配置在 `<style>` 标签中。

## 🔧 开发指南

### 项目结构

```
zuojian-image/
├── index.html          # 主应用文件
├── README.md          # 项目说明文档
└── .git/              # Git 版本控制
```

### 核心功能模块

1. **消息处理**: `displayUserMessage()` 和 `displayBotMessage()`
2. **API 调用**: Dify API 集成和错误处理
3. **会话管理**: 本地存储会话 ID 和用户 ID
4. **内容解析**: `parseDifyResponse()` 分离文本和图片
5. **界面交互**: 输入框自适应、滚动控制等

### 添加新功能

要添加新功能，可以在 JavaScript 部分扩展相应的函数。例如：

- 添加新的消息类型处理
- 扩展 API 响应解析逻辑
- 增加用户界面组件

## 🐛 故障排除

### 常见问题

1. **API 调用失败**
   - 检查 API Key 是否正确
   - 确认网络连接正常
   - 查看浏览器控制台错误信息

2. **图片无法显示**
   - 确认图片 URL 有效
   - 检查网络连接
   - 查看浏览器安全策略

3. **会话丢失**
   - 检查浏览器本地存储是否被清除
   - 确认会话 ID 有效性

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- GitHub Issues: [https://github.com/jememouse/zuojian-image/issues](https://github.com/jememouse/zuojian-image/issues)
- 项目主页: [https://github.com/jememouse/zuojian-image](https://github.com/jememouse/zuojian-image)

---

⭐ 如果这个项目对您有帮助，请给它一个星标！
